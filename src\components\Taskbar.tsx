
import React, { useState, useEffect } from 'react';

interface TaskbarProps {
  windows: Array<{
    id: string;
    title: string;
    isMinimized: boolean;
    isOpen: boolean;
  }>;
  onWindowClick: (id: string) => void;
  onOpenApp: (title: string, component: string) => void;
}

const Taskbar: React.FC<TaskbarProps> = ({ windows, onWindowClick, onOpenApp }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  // const [showStartMenu, setShowStartMenu] = useState(false); // TODO: implement start menu

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // console.log('Taskbar render, windows:', windows.length); // debug

  const formatTime = (date: Date) => {
    // I prefer 12-hour format
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 h-16 bg-black/30 backdrop-blur-lg border-t border-white/20 flex items-center px-4 z-50">
      {/* Start Menu Button */}
      <div className="flex items-center space-x-4">
        <button
          className="w-10 h-10 bg-blue-500 hover:bg-blue-600 rounded-lg flex items-center justify-center transition-colors"
          onClick={() => {
            // TODO: implement start menu functionality
            console.log('Start menu clicked - not implemented yet');
          }}
        >
          <span className="text-white font-bold">⊞</span>
        </button>

        {/* Quick Launch Icons */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onOpenApp('File Explorer', 'FileExplorer')}
            className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-lg flex items-center justify-center transition-colors"
            title="File Explorer"
          >
            <span className="text-xl">📁</span>
          </button>
          <button
            onClick={() => onOpenApp('Notepad', 'Notepad')}
            className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-lg flex items-center justify-center transition-colors"
            title="Notepad"
          >
            <span className="text-xl">📝</span>
          </button>
          <button
            onClick={() => onOpenApp('Terminal', 'Terminal')}
            className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-lg flex items-center justify-center transition-colors"
            title="Terminal"
          >
            <span className="text-xl">💻</span>
          </button>
        </div>
      </div>

      {/* Window Indicators */}
      <div className="flex-1 flex items-center justify-center space-x-2 mx-4">
        {windows.filter(w => w.isOpen).map(window => (
          <button
            key={window.id}
            onClick={() => onWindowClick(window.id)}
            className={`px-4 py-2 rounded-lg text-white font-medium transition-all duration-200 ${
              window.isMinimized 
                ? 'bg-white/20 hover:bg-white/30' 
                : 'bg-blue-500/50 hover:bg-blue-500/70 border border-blue-300/50'
            }`}
          >
            {window.title}
          </button>
        ))}
      </div>

      {/* System Tray */}
      <div className="flex items-center space-x-4">
        {/* Notification Icons */}
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-white/20 rounded flex items-center justify-center">
            <span className="text-xs text-white">🔊</span>
          </div>
          <div className="w-6 h-6 bg-white/20 rounded flex items-center justify-center">
            <span className="text-xs text-white">📶</span>
          </div>
          <div className="w-6 h-6 bg-white/20 rounded flex items-center justify-center">
            <span className="text-xs text-white">🔋</span>
          </div>
        </div>

        {/* Clock */}
        <div className="text-white text-right">
          <div className="text-sm font-medium">{formatTime(currentTime)}</div>
          <div className="text-xs opacity-75">{formatDate(currentTime)}</div>
        </div>
      </div>
    </div>
  );
};

export default Taskbar;
