
import React, { useState } from 'react';

const Notepad: React.FC = () => {
  const [content, setContent] = useState('Welcome to Notepad!\n\nStart typing your notes here...');
  const [fontSize, setFontSize] = useState(14);
  const [wordWrap, setWordWrap] = useState(true);

  const handleSave = () => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'document.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleLoad = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setContent(e.target?.result as string);
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  return (
    <div className="h-full flex flex-col">
      {/* Menu Bar */}
      <div className="bg-gray-100 border-b px-4 py-2 flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => setContent('')}
            className="px-2 py-1 text-sm hover:bg-gray-200 rounded"
          >
            New
          </button>
          <button 
            onClick={handleLoad}
            className="px-2 py-1 text-sm hover:bg-gray-200 rounded"
          >
            Open
          </button>
          <button 
            onClick={handleSave}
            className="px-2 py-1 text-sm hover:bg-gray-200 rounded"
          >
            Save
          </button>
        </div>
        
        <div className="border-l border-gray-300 h-4" />
        
        <div className="flex items-center space-x-2">
          <label className="text-sm">Font Size:</label>
          <select 
            value={fontSize} 
            onChange={(e) => setFontSize(Number(e.target.value))}
            className="px-2 py-1 text-sm border rounded"
          >
            <option value={10}>10</option>
            <option value={12}>12</option>
            <option value={14}>14</option>
            <option value={16}>16</option>
            <option value={18}>18</option>
            <option value={20}>20</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          <input 
            type="checkbox" 
            id="wordWrap" 
            checked={wordWrap}
            onChange={(e) => setWordWrap(e.target.checked)}
          />
          <label htmlFor="wordWrap" className="text-sm">Word Wrap</label>
        </div>
      </div>

      {/* Text Editor */}
      <div className="flex-1 p-4">
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          className="w-full h-full resize-none border-none outline-none font-mono"
          style={{ 
            fontSize: `${fontSize}px`,
            whiteSpace: wordWrap ? 'pre-wrap' : 'pre'
          }}
          placeholder="Start typing..."
        />
      </div>

      {/* Status Bar */}
      <div className="bg-gray-100 border-t px-4 py-2 text-sm text-gray-600 flex justify-between">
        <span>Characters: {content.length}</span>
        <span>Lines: {content.split('\n').length}</span>
      </div>
    </div>
  );
};

export default Notepad;
