
import React, { useState, useRef, useEffect } from 'react';
// import { useCallback } from 'react'; // might need this for optimization later

interface WindowProps {
  window: {
    id: string;
    title: string;
    isMaximized: boolean;
    position: { x: number; y: number };
    size: { width: number; height: number };
    zIndex: number;
  };
  children: React.ReactNode;
  onClose: () => void;
  onMinimize: () => void;
  onMaximize: () => void;
  onFocus: () => void;
  onPositionChange: (position: { x: number; y: number }) => void;
  onSizeChange: (size: { width: number; height: number }) => void;
}

const Window: React.FC<WindowProps> = ({
  window,
  children,
  onClose,
  onMinimize,
  onMaximize,
  onFocus,
  onPositionChange,
  onSizeChange
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const windowRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    // only start dragging if clicking on header
    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('window-header')) {
      setIsDragging(true);
      setDragOffset({
        x: e.clientX - window.position.x,
        y: e.clientY - window.position.y
      });
      onFocus();
    }
  };

  const handleResizeMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsResizing(true);
    onFocus();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging && !window.isMaximized) {
        // keep window within screen bounds (mostly)
        let newX = e.clientX - dragOffset.x;
        let newY = e.clientY - dragOffset.y;

        // FIXME: this boundary checking is kinda janky but works for now
        newX = Math.max(-window.size.width + 100, Math.min(newX, window.innerWidth - 100)); // allow some off-screen
        newY = Math.max(0, Math.min(newY, window.innerHeight - window.size.height - 60)); // 60px for taskbar

        onPositionChange({ x: newX, y: newY });
      }
      
      if (isResizing && !window.isMaximized) {
        const rect = windowRef.current?.getBoundingClientRect();
        if (rect) {
          // minimum sizes to prevent weird UI issues
          const newWidth = Math.max(300, e.clientX - rect.left);
          const newHeight = Math.max(200, e.clientY - rect.top);
          // TODO: add maximum size constraints too
          onSizeChange({ width: newWidth, height: newHeight });
        }
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
    };

    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, isResizing, dragOffset, window.isMaximized, window.size, onPositionChange, onSizeChange]);

  const windowStyle = window.isMaximized
    ? { 
        top: 0, 
        left: 0, 
        width: '100vw', 
        height: 'calc(100vh - 60px)',
        zIndex: window.zIndex
      }
    : {
        top: window.position.y,
        left: window.position.x,
        width: window.size.width,
        height: window.size.height,
        zIndex: window.zIndex
      };

  return (
    <div
      ref={windowRef}
      className="absolute bg-white/95 backdrop-blur-md rounded-lg shadow-2xl border border-white/30 overflow-hidden animate-scale-in"
      style={windowStyle}
      onMouseDown={onFocus}
    >
      {/* Window Header */}
      <div
        className="window-header bg-gradient-to-r from-blue-500/80 to-purple-500/80 text-white px-4 py-2 flex items-center justify-between cursor-move select-none"
        onMouseDown={handleMouseDown}
        onDoubleClick={onMaximize}
      >
        <span className="font-medium">{window.title}</span>
        <div className="flex items-center space-x-2">
          <button
            onClick={onMinimize}
            className="w-3 h-3 bg-yellow-400 rounded-full hover:bg-yellow-500 transition-colors"
            aria-label="Minimize"
          />
          <button
            onClick={onMaximize}
            className="w-3 h-3 bg-green-400 rounded-full hover:bg-green-500 transition-colors"
            aria-label="Maximize"
          />
          <button
            onClick={onClose}
            className="w-3 h-3 bg-red-400 rounded-full hover:bg-red-500 transition-colors"
            aria-label="Close"
          />
        </div>
      </div>

      {/* Window Content */}
      <div className="h-full pb-12 overflow-auto">
        {children}
      </div>

      {/* Resize Handle */}
      {!window.isMaximized && (
        <div
          className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize"
          onMouseDown={handleResizeMouseDown}
        >
          <div className="absolute bottom-1 right-1 w-2 h-2 bg-gray-400 transform rotate-45" />
        </div>
      )}
    </div>
  );
};

export default Window;
