
import React from 'react';

interface ContextMenuProps {
  position: { x: number; y: number };
  onCreateFolder: () => void;
  onChangeWallpaper: (theme: string) => void;
  currentTheme: string;
}

const ContextMenu: React.FC<ContextMenuProps> = ({ 
  position, 
  onCreateFolder, 
  onChangeWallpaper,
  currentTheme 
}) => {
  const themes = [
    { name: 'Default', value: 'default' },
    { name: 'Dark', value: 'dark' },
    { name: 'Light', value: 'light' },
    { name: 'macOS', value: 'macos' }
  ];

  return (
    <div
      className="fixed bg-white/90 backdrop-blur-md rounded-lg shadow-xl border border-white/30 py-2 z-50 min-w-48 animate-fade-in"
      style={{ left: position.x, top: position.y }}
      onClick={(e) => e.stopPropagation()}
    >
      <button
        onClick={onCreateFolder}
        className="w-full text-left px-4 py-2 hover:bg-blue-500/20 flex items-center space-x-3 transition-colors"
      >
        <span>📁</span>
        <span>Create Folder</span>
      </button>
      
      <div className="border-t border-gray-200/50 my-1" />
      
      <div className="px-4 py-2">
        <div className="text-sm font-medium text-gray-700 mb-2">Change Wallpaper</div>
        {themes.map(theme => (
          <button
            key={theme.value}
            onClick={() => onChangeWallpaper(theme.value)}
            className={`w-full text-left px-2 py-1 rounded text-sm hover:bg-blue-500/20 transition-colors ${
              currentTheme === theme.value ? 'bg-blue-500/30 font-medium' : ''
            }`}
          >
            {theme.name}
          </button>
        ))}
      </div>
      
      <div className="border-t border-gray-200/50 my-1" />
      
      <button className="w-full text-left px-4 py-2 hover:bg-blue-500/20 flex items-center space-x-3 transition-colors">
        <span>🔄</span>
        <span>Refresh</span>
      </button>
      
      <button className="w-full text-left px-4 py-2 hover:bg-blue-500/20 flex items-center space-x-3 transition-colors">
        <span>⚙️</span>
        <span>Desktop Settings</span>
      </button>
    </div>
  );
};

export default ContextMenu;
