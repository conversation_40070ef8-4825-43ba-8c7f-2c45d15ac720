# Desktop Simulator

Just a fun little project I've been working on - trying to recreate that classic Windows desktop experience in the browser.

## What is this?

It's basically a web-based desktop environment that mimics Windows. You can open windows, drag them around, minimize/maximize, use a file explorer, notepad, and even a terminal (well, sort of).

Started this as a weekend project and it kinda grew from there. Still lots of rough edges but it's pretty fun to play with!

## Running it locally

Pretty standard React setup:

```bash
npm install
npm run dev
```

That's it. Should open up on localhost:5173 or whatever port Vite picks.

## Tech Stack

- React + TypeScript (because I like type safety)
- Tailwind for styling (yeah I know, everyone uses it now)
- Vite for the build tool
- Some shadcn components here and there

## Features

- [x] Draggable windows
- [x] Window management (min/max/close)
- [x] File explorer with basic navigation
- [x] Simple notepad
- [x] Fake terminal (doesn't actually do anything useful yet)
- [x] Context menu on desktop
- [x] Taskbar with clock
- [ ] Actually useful terminal
- [ ] More apps
- [ ] Better file system simulation
- [ ] Maybe some games?

## Known Issues

- Resizing can be a bit janky sometimes
- No persistence - everything resets on refresh
- Terminal is just for show right now
- Probably breaks on mobile (haven't tested much)

Feel free to mess around with it or contribute if you want!
