
import React, { useState, useEffect, useRef } from 'react';
import Taskbar from './Taskbar';
import Window from './Window';
import ContextMenu from './ContextMenu';
import FileExplorer from './FileExplorer';
import Notepad from './Notepad';
import Terminal from './Terminal';

interface WindowState {
  id: string;
  title: string;
  component: string;
  isOpen: boolean;
  isMinimized: boolean;
  isMaximized: boolean;
  position: { x: number; y: number };
  size: { width: number; height: number };
  zIndex: number;
}

interface FileSystemItem {
  id: string;
  name: string;
  type: 'file' | 'folder';
  content?: string;
  children?: FileSystemItem[];
}

const Desktop = () => {
  const [windows, setWindows] = useState<WindowState[]>([]);
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number } | null>(null);
  const [theme, setTheme] = useState('default');
  const [nextZIndex, setNextZIndex] = useState(1000);
  const [fileSystem, setFileSystem] = useState<FileSystemItem[]>([
    {
      id: '1',
      name: 'Documents',
      type: 'folder',
      children: [
        { id: '2', name: 'readme.txt', type: 'file', content: 'Welcome to the desktop environment!' },
        { id: '3', name: 'notes.txt', type: 'file', content: 'My personal notes...' }
      ]
    },
    {
      id: '4',
      name: 'Pictures',
      type: 'folder',
      children: []
    },
    { id: '5', name: 'desktop-info.txt', type: 'file', content: 'This is a simulated file system!' }
  ]);

  const desktopRef = useRef<HTMLDivElement>(null);

  const openWindow = (title: string, component: string) => {
    const existingWindow = windows.find(w => w.title === title);
    
    if (existingWindow) {
      if (existingWindow.isMinimized) {
        setWindows(prev => prev.map(w => 
          w.id === existingWindow.id 
            ? { ...w, isMinimized: false, zIndex: nextZIndex }
            : w
        ));
        setNextZIndex(prev => prev + 1);
      } else {
        focusWindow(existingWindow.id);
      }
      return;
    }

    const newWindow: WindowState = {
      id: Date.now().toString(),
      title,
      component,
      isOpen: true,
      isMinimized: false,
      isMaximized: false,
      position: { x: Math.random() * 200 + 100, y: Math.random() * 100 + 50 },
      size: { width: 600, height: 400 },
      zIndex: nextZIndex
    };

    setWindows(prev => [...prev, newWindow]);
    setNextZIndex(prev => prev + 1);
  };

  const closeWindow = (id: string) => {
    setWindows(prev => prev.filter(w => w.id !== id));
  };

  const minimizeWindow = (id: string) => {
    setWindows(prev => prev.map(w => 
      w.id === id ? { ...w, isMinimized: true } : w
    ));
  };

  const maximizeWindow = (id: string) => {
    setWindows(prev => prev.map(w => 
      w.id === id ? { 
        ...w, 
        isMaximized: !w.isMaximized,
        position: w.isMaximized ? w.position : { x: 0, y: 0 },
        size: w.isMaximized ? w.size : { width: window.innerWidth, height: window.innerHeight - 60 }
      } : w
    ));
  };

  const focusWindow = (id: string) => {
    setWindows(prev => prev.map(w => 
      w.id === id ? { ...w, zIndex: nextZIndex } : w
    ));
    setNextZIndex(prev => prev + 1);
  };

  const updateWindowPosition = (id: string, position: { x: number; y: number }) => {
    setWindows(prev => prev.map(w => 
      w.id === id ? { ...w, position } : w
    ));
  };

  const updateWindowSize = (id: string, size: { width: number; height: number }) => {
    setWindows(prev => prev.map(w => 
      w.id === id ? { ...w, size } : w
    ));
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setContextMenu({ x: e.clientX, y: e.clientY });
  };

  const closeContextMenu = () => {
    setContextMenu(null);
  };

  const createNewFolder = () => {
    const newFolder: FileSystemItem = {
      id: Date.now().toString(),
      name: 'New Folder',
      type: 'folder',
      children: []
    };
    setFileSystem(prev => [...prev, newFolder]);
    closeContextMenu();
  };

  const changeWallpaper = (newTheme: string) => {
    setTheme(newTheme);
    closeContextMenu();
  };

  const renderWindowContent = (window: WindowState) => {
    switch (window.component) {
      case 'FileExplorer':
        return <FileExplorer fileSystem={fileSystem} onOpenFile={(content) => openWindow('Notepad', 'Notepad')} />;
      case 'Notepad':
        return <Notepad />;
      case 'Terminal':
        return <Terminal />;
      default:
        return <div className="p-4">Unknown application</div>;
    }
  };

  useEffect(() => {
    const handleClick = () => closeContextMenu();
    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, []);

  const getThemeClasses = () => {
    switch (theme) {
      case 'dark':
        return 'bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900';
      case 'light':
        return 'bg-gradient-to-br from-blue-100 via-purple-50 to-pink-100';
      case 'macos':
        return 'bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500';
      default:
        return 'bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800';
    }
  };

  return (
    <div 
      ref={desktopRef}
      className={`min-h-screen relative overflow-hidden select-none ${getThemeClasses()}`}
      onContextMenu={handleContextMenu}
      onClick={closeContextMenu}
    >
      {/* Desktop Icons */}
      <div className="absolute top-4 left-4 space-y-4">
        <div 
          className="flex flex-col items-center cursor-pointer group"
          onDoubleClick={() => openWindow('File Explorer', 'FileExplorer')}
        >
          <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mb-2 group-hover:bg-white/30 transition-all duration-200">
            <span className="text-2xl">📁</span>
          </div>
          <span className="text-white text-sm font-medium">File Explorer</span>
        </div>
        
        <div 
          className="flex flex-col items-center cursor-pointer group"
          onDoubleClick={() => openWindow('Notepad', 'Notepad')}
        >
          <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mb-2 group-hover:bg-white/30 transition-all duration-200">
            <span className="text-2xl">📝</span>
          </div>
          <span className="text-white text-sm font-medium">Notepad</span>
        </div>
        
        <div 
          className="flex flex-col items-center cursor-pointer group"
          onDoubleClick={() => openWindow('Terminal', 'Terminal')}
        >
          <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center mb-2 group-hover:bg-white/30 transition-all duration-200">
            <span className="text-2xl">💻</span>
          </div>
          <span className="text-white text-sm font-medium">Terminal</span>
        </div>
      </div>

      {/* Windows */}
      {windows.filter(w => w.isOpen && !w.isMinimized).map(window => (
        <Window
          key={window.id}
          window={window}
          onClose={() => closeWindow(window.id)}
          onMinimize={() => minimizeWindow(window.id)}
          onMaximize={() => maximizeWindow(window.id)}
          onFocus={() => focusWindow(window.id)}
          onPositionChange={(position) => updateWindowPosition(window.id, position)}
          onSizeChange={(size) => updateWindowSize(window.id, size)}
        >
          {renderWindowContent(window)}
        </Window>
      ))}

      {/* Context Menu */}
      {contextMenu && (
        <ContextMenu
          position={contextMenu}
          onCreateFolder={createNewFolder}
          onChangeWallpaper={changeWallpaper}
          currentTheme={theme}
        />
      )}

      {/* Taskbar */}
      <Taskbar
        windows={windows}
        onWindowClick={(id) => {
          const window = windows.find(w => w.id === id);
          if (window?.isMinimized) {
            setWindows(prev => prev.map(w => 
              w.id === id ? { ...w, isMinimized: false, zIndex: nextZIndex } : w
            ));
            setNextZIndex(prev => prev + 1);
          } else {
            focusWindow(id);
          }
        }}
        onOpenApp={openWindow}
      />
    </div>
  );
};

export default Desktop;
