
import React, { useState } from 'react';

interface FileSystemItem {
  id: string;
  name: string;
  type: 'file' | 'folder';
  content?: string;
  children?: FileSystemItem[];
}

interface FileExplorerProps {
  fileSystem: FileSystemItem[];
  onOpenFile: (content: string) => void;
}

const FileExplorer: React.FC<FileExplorerProps> = ({ fileSystem, onOpenFile }) => {
  const [currentPath, setCurrentPath] = useState<string[]>([]);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  // const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid'); // TODO: implement list view

  const getCurrentItems = (): FileSystemItem[] => {
    let items = fileSystem;
    for (const pathSegment of currentPath) {
      const folder = items.find(item => item.name === pathSegment && item.type === 'folder');
      if (folder && folder.children) {
        items = folder.children;
      }
    }
    return items;
  };

  const handleItemDoubleClick = (item: FileSystemItem) => {
    if (item.type === 'folder') {
      setCurrentPath(prev => [...prev, item.name]);
      setSelectedItem(null);
    } else if (item.content) {
      onOpenFile(item.content);
    }
  };

  const handleBack = () => {
    if (currentPath.length > 0) {
      setCurrentPath(prev => prev.slice(0, -1));
      setSelectedItem(null);
    }
  };

  const currentItems = getCurrentItems();
  const currentPathString = currentPath.length === 0 ? 'Desktop' : currentPath.join(' > ');

  return (
    <div className="h-full flex flex-col">
      {/* Navigation Bar */}
      <div className="bg-gray-100 border-b px-4 py-2 flex items-center space-x-2">
        <button
          onClick={handleBack}
          disabled={currentPath.length === 0}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          ← Back
        </button>
        <div className="flex-1 bg-white border rounded px-3 py-1 text-sm">
          {currentPathString}
        </div>
      </div>

      {/* Toolbar */}
      <div className="bg-gray-50 border-b px-4 py-2 flex items-center space-x-4">
        <button
          className="flex items-center space-x-1 text-sm hover:text-blue-600"
          onClick={() => {
            // TODO: implement new folder creation
            console.log('New folder clicked - not implemented');
          }}
        >
          <span>📁</span>
          <span>New Folder</span>
        </button>
        <button
          className="flex items-center space-x-1 text-sm hover:text-blue-600"
          onClick={() => {
            // TODO: implement new file creation
            console.log('New file clicked - not implemented');
          }}
        >
          <span>📄</span>
          <span>New File</span>
        </button>
        <div className="border-l border-gray-300 h-4" />
        <button className="flex items-center space-x-1 text-sm hover:text-blue-600 opacity-50 cursor-not-allowed">
          <span>📋</span>
          <span>Paste</span>
        </button>
      </div>

      {/* File List */}
      <div className="flex-1 p-4">
        <div className="grid grid-cols-4 gap-4">
          {currentItems.map(item => (
            <div
              key={item.id}
              className={`flex flex-col items-center p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                selectedItem === item.id 
                  ? 'bg-blue-100 border-2 border-blue-500' 
                  : 'hover:bg-gray-100 border-2 border-transparent'
              }`}
              onClick={() => setSelectedItem(item.id)}
              onDoubleClick={() => handleItemDoubleClick(item)}
            >
              <div className="text-4xl mb-2">
                {item.type === 'folder' ? '📁' : '📄'}
              </div>
              <span className="text-sm text-center text-gray-700 break-words max-w-full">
                {item.name}
              </span>
            </div>
          ))}
        </div>

        {currentItems.length === 0 && (
          <div className="flex items-center justify-center h-32 text-gray-500">
            <div className="text-center">
              <div className="text-4xl mb-2">📂</div>
              <div>This folder is empty</div>
            </div>
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="bg-gray-100 border-t px-4 py-2 text-sm text-gray-600">
        {currentItems.length} items
      </div>
    </div>
  );
};

export default FileExplorer;
