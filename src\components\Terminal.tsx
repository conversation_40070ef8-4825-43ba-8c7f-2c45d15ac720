
import React, { useState, useRef, useEffect } from 'react';

interface TerminalLine {
  type: 'input' | 'output' | 'error';
  content: string;
}

const Terminal: React.FC = () => {
  const [lines, setLines] = useState<TerminalLine[]>([
    { type: 'output', content: 'Desktop OS Terminal v1.0' },
    { type: 'output', content: 'Type "help" for available commands.' },
    { type: 'output', content: '' }
  ]);
  const [currentInput, setCurrentInput] = useState('');
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const terminalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // TODO: add more commands like 'cat', 'mkdir', 'rm', etc.
  // TODO: maybe implement a fake file system?

  const commands = {
    help: () => [
      'Available commands:',
      '  help     - Show this help message',
      '  clear    - Clear the terminal',
      '  date     - Show current date and time',
      '  echo     - Echo text back',
      '  ls       - List files (simulated)',
      '  pwd      - Show current directory',
      '  whoami   - Show current user',
      '  calc     - Simple calculator (e.g., calc 2 + 3)',
      '  cowsay   - Make the cow say something',
      ''
    ],
    clear: () => {
      setLines([]);
      return [];
    },
    date: () => [new Date().toString()],
    ls: () => ['Documents/', 'Pictures/', 'desktop-info.txt', 'readme.txt'],
    pwd: () => ['/home/<USER>/Desktop'],
    whoami: () => ['user'],
    echo: (args: string[]) => [args.join(' ')],
    calc: (args: string[]) => {
      try {
        const expression = args.join(' ');
        if (!expression.trim()) {
          return ['Error: No expression provided'];
        }
        // Simple math evaluation (only allow numbers and basic operators)
        if (!/^[0-9+\-*/.() ]+$/.test(expression)) {
          return ['Error: Invalid characters in expression'];
        }
        // HACK: using Function constructor is kinda sketchy but works for simple math
        const result = Function(`"use strict"; return (${expression})`)();
        if (typeof result !== 'number' || !isFinite(result)) {
          return ['Error: Result is not a valid number'];
        }
        return [result.toString()];
      } catch (error) {
        return ['Error: Invalid expression'];
      }
    },
    cowsay: (args: string[]) => {
      const message = args.join(' ') || 'Hello from the terminal!';
      const border = '_'.repeat(message.length + 2);
      return [
        ` ${border}`,
        `< ${message} >`,
        ` ${'-'.repeat(message.length + 2)}`,
        '        \\   ^__^',
        '         \\  (oo)\\_______',
        '            (__)\\       )\\/\\',
        '                ||----w |',
        '                ||     ||'
      ];
    }
  };

  const executeCommand = (command: string) => {
    const [cmd, ...args] = command.trim().split(' ');

    if (!cmd) return [];

    // console.log('Executing command:', cmd, 'with args:', args); // debug

    if (commands[cmd as keyof typeof commands]) {
      return commands[cmd as keyof typeof commands](args);
    } else {
      return [`Command not found: ${cmd}. Type "help" for available commands.`];
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentInput.trim()) return;

    // Add command to history
    setCommandHistory(prev => [...prev, currentInput]);
    setHistoryIndex(-1);

    // Add input line
    setLines(prev => [...prev, { type: 'input', content: `$ ${currentInput}` }]);

    // Execute command and add output
    const output = executeCommand(currentInput);
    if (output.length > 0) {
      setLines(prev => [...prev, ...output.map(line => ({ type: 'output' as const, content: line }))]);
    }

    // Add empty line for spacing
    setLines(prev => [...prev, { type: 'output', content: '' }]);

    setCurrentInput('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 
          ? commandHistory.length - 1 
          : Math.max(0, historyIndex - 1);
        setHistoryIndex(newIndex);
        setCurrentInput(commandHistory[newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex > -1) {
        const newIndex = historyIndex + 1;
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1);
          setCurrentInput('');
        } else {
          setHistoryIndex(newIndex);
          setCurrentInput(commandHistory[newIndex]);
        }
      }
    }
  };

  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [lines]);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  return (
    <div 
      className="h-full bg-black text-green-400 font-mono text-sm flex flex-col"
      onClick={() => inputRef.current?.focus()}
    >
      {/* Terminal Output */}
      <div 
        ref={terminalRef}
        className="flex-1 p-4 overflow-y-auto"
      >
        {lines.map((line, index) => (
          <div key={index} className={`${
            line.type === 'input' ? 'text-yellow-400' : 
            line.type === 'error' ? 'text-red-400' : 
            'text-green-400'
          }`}>
            {line.content}
          </div>
        ))}
        
        {/* Current Input Line */}
        <form onSubmit={handleSubmit} className="flex items-center">
          <span className="text-yellow-400 mr-2">$</span>
          <input
            ref={inputRef}
            type="text"
            value={currentInput}
            onChange={(e) => setCurrentInput(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1 bg-transparent outline-none text-green-400 font-mono"
            autoComplete="off"
            spellCheck={false}
          />
        </form>
      </div>
    </div>
  );
};

export default Terminal;
